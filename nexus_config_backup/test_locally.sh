#!/bin/bash
# Local test script for Nexus configuration collection using Ansible

# Install required packages
echo "Installing required packages..."
pip install --upgrade pip
pip install -r requirements.txt

# Install Ansible collection for Cisco NXOS
echo "Installing Ansible collection for Cisco NXOS..."
ansible-galaxy collection install cisco.nxos

# Create directory for configurations
echo "Creating directory for configurations..."
DATETIME=$(date +"%Y%m%d_%H%M%S")
mkdir -p "configs/${DATETIME}"

# Prompt for credentials
read -p "Enter username: " USERNAME
read -s -p "Enter password: " PASSWORD
echo ""

# Export credentials as environment variables for Ansible
export ANSIBLE_USER="${USERNAME}"
export ANSIBLE_PASSWORD="${PASSWORD}"

# Run the Ansible playbook
echo "Collecting configurations..."
ansible-playbook collect_nexus_configs.yml -e "output_dir=configs"

# Check if any configurations were collected
CONFIG_COUNT=$(find "configs/${DATETIME}" -type f -name "*.txt" | wc -l)
if [ $CONFIG_COUNT -eq 0 ]; then
    echo "No configurations were collected."
    exit 1
else
    echo "Successfully collected ${CONFIG_COUNT} configuration files."
fi
