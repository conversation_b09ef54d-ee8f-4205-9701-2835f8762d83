#!/usr/bin/env python3
"""
Nexus Configuration Collection Script

This script uses PyATS/Genie to connect to Cisco Nexus devices and collect
their running configurations, then saves them to files.
"""

import os
import sys
import argparse
import logging
from datetime import datetime
from pyats.topology import loader
from pyats.utils.secret_strings import to_plaintext

# Set up logging
logging.basicConfig(level=logging.INFO, 
                    format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Collect Cisco Nexus configurations')
    
    parser.add_argument('--testbed', type=str, default='testbed.yaml',
                        help='Path to the testbed YAML file (default: testbed.yaml)')
    parser.add_argument('--username', type=str, required=False,
                        help='Username for device authentication (overrides testbed)')
    parser.add_argument('--password', type=str, required=False,
                        help='Password for device authentication (overrides testbed)')
    parser.add_argument('--output-dir', type=str, default='configs',
                        help='Directory to store configuration files')
    
    return parser.parse_args()

def collect_device_config(device, output_dir):
    """Connect to device and collect its running configuration."""
    try:
        # Connect to the device
        logger.info(f"Connecting to device: {device.name}")
        device.connect()
        
        # Collect running configuration
        logger.info(f"Collecting configuration from {device.name}")
        config = device.execute('show running-config')
        
        # Create filename with device name and timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{device.name}_{timestamp}.txt"
        file_path = os.path.join(output_dir, filename)
        
        # Write configuration to file
        with open(file_path, 'w') as f:
            f.write(config)
        
        logger.info(f"Configuration saved to {file_path}")
        
        # Also collect 'show version' output for inventory purposes
        version_info = device.execute('show version')
        version_file_path = os.path.join(output_dir, f"{device.name}_{timestamp}_version.txt")
        
        with open(version_file_path, 'w') as f:
            f.write(version_info)
            
        logger.info(f"Version information saved to {version_file_path}")
        
        # Disconnect from device
        device.disconnect()
        return True
        
    except Exception as e:
        logger.error(f"Error collecting configuration from {device.name}: {str(e)}")
        return False

def main():
    """Main function."""
    args = parse_arguments()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # Load the testbed file
        logger.info(f"Loading testbed file: {args.testbed}")
        testbed = loader.load(args.testbed)
        
        # Override credentials if provided
        if args.username and args.password:
            logger.info("Using provided credentials")
            for device in testbed.devices:
                testbed.devices[device].credentials.default.username = args.username
                testbed.devices[device].credentials.default.password = to_plaintext(args.password)
        
        success_count = 0
        failure_count = 0
        
        # Iterate through all devices in the testbed
        for device_name, device in testbed.devices.items():
            if device.os in ['nxos', 'nxos-ssh']:
                result = collect_device_config(device, args.output_dir)
                if result:
                    success_count += 1
                else:
                    failure_count += 1
            else:
                logger.warning(f"Skipping non-Nexus device: {device_name} (OS: {device.os})")
        
        logger.info(f"Configuration collection complete: {success_count} successful, {failure_count} failed")
        
        if failure_count > 0:
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"Error: {str(e)}")
        sys.exit(1)

if __name__ == "__main__":
    main()
