# Ansible inventory for Cisco Nexus CLOS topology with 2 spines and 6 leafs

[all:vars]
ansible_connection=network_cli
ansible_network_os=nxos
ansible_user={{ lookup('env', 'ANSIBLE_USER') | default('admin') }}
ansible_password={{ lookup('env', 'ANSIBLE_PASSWORD') | default('admin') }}
ansible_become=no
ansible_ssh_common_args='-o StrictHostKeyChecking=no'

[spines]
spine1 ansible_host=*************
spine2 ansible_host=*************

[leafs]
leaf1 ansible_host=*************
leaf2 ansible_host=*************
leaf3 ansible_host=*************
leaf4 ansible_host=*************
leaf5 ansible_host=*************
leaf6 ansible_host=*************

[nexus:children]
spines
leafs
