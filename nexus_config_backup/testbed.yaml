---
# Testbed file for Cisco Nexus CLOS topology with 2 spines and 6 leafs
testbed:
  name: nexus-clos-fabric
  credentials:
    default:
      username: admin
      password: "%ENV{DEVICE_PASSWORD}"

devices:
  spine1:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: spine
      
  spine2:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: spine
      
  leaf1:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: leaf
      
  leaf2:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: leaf
      
  leaf3:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: leaf
      
  leaf4:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: leaf
      
  leaf5:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: leaf
      
  leaf6:
    os: nxos
    type: switch
    connections:
      cli:
        ip: *************
        protocol: ssh
    custom:
      role: leaf

topology:
  spine1:
    interfaces:
      Ethernet1/1:
        link: spine1-leaf1
        type: ethernet
      Ethernet1/2:
        link: spine1-leaf2
        type: ethernet
      Ethernet1/3:
        link: spine1-leaf3
        type: ethernet
      Ethernet1/4:
        link: spine1-leaf4
        type: ethernet
      Ethernet1/5:
        link: spine1-leaf5
        type: ethernet
      Ethernet1/6:
        link: spine1-leaf6
        type: ethernet
  
  spine2:
    interfaces:
      Ethernet1/1:
        link: spine2-leaf1
        type: ethernet
      Ethernet1/2:
        link: spine2-leaf2
        type: ethernet
      Ethernet1/3:
        link: spine2-leaf3
        type: ethernet
      Ethernet1/4:
        link: spine2-leaf4
        type: ethernet
      Ethernet1/5:
        link: spine2-leaf5
        type: ethernet
      Ethernet1/6:
        link: spine2-leaf6
        type: ethernet

  leaf1:
    interfaces:
      Ethernet1/1:
        link: spine1-leaf1
        type: ethernet
      Ethernet1/2:
        link: spine2-leaf1
        type: ethernet
        
  leaf2:
    interfaces:
      Ethernet1/1:
        link: spine1-leaf2
        type: ethernet
      Ethernet1/2:
        link: spine2-leaf2
        type: ethernet
        
  leaf3:
    interfaces:
      Ethernet1/1:
        link: spine1-leaf3
        type: ethernet
      Ethernet1/2:
        link: spine2-leaf3
        type: ethernet
        
  leaf4:
    interfaces:
      Ethernet1/1:
        link: spine1-leaf4
        type: ethernet
      Ethernet1/2:
        link: spine2-leaf4
        type: ethernet
        
  leaf5:
    interfaces:
      Ethernet1/1:
        link: spine1-leaf5
        type: ethernet
      Ethernet1/2:
        link: spine2-leaf5
        type: ethernet
        
  leaf6:
    interfaces:
      Ethernet1/1:
        link: spine1-leaf6
        type: ethernet
      Ethernet1/2:
        link: spine2-leaf6
        type: ethernet
