---
# Ansible playbook to collect Cisco Nexus configurations
- name: Collect Nexus Configurations
  hosts: all
  gather_facts: no
  
  vars:
    config_dir: "{{ output_dir | default('configs') }}"
    datetime: "{{ lookup('pipe', 'date +%Y%m%d_%H%M%S') }}"
    
  tasks:
    - name: Create output directory
      delegate_to: localhost
      file:
        path: "{{ config_dir }}/{{ datetime }}"
        state: directory
      run_once: true
      
    - name: Collect running configuration
      nxos_command:
        commands: show running-config
      register: config_output
      
    - name: Save running configuration to file
      delegate_to: localhost
      copy:
        content: "{{ config_output.stdout[0] }}"
        dest: "{{ config_dir }}/{{ datetime }}/{{ inventory_hostname }}_running-config.txt"
        
    - name: Collect version information
      nxos_command:
        commands: show version
      register: version_output
      
    - name: Save version information to file
      delegate_to: localhost
      copy:
        content: "{{ version_output.stdout[0] }}"
        dest: "{{ config_dir }}/{{ datetime }}/{{ inventory_hostname }}_version.txt"
