# Nexus Configuration Backup

This project provides an automated solution to back up Cisco Nexus switch configurations from a CLOS topology network to a Git repository using Jenkins and Ansible.

## Architecture

- **Jenkins Pipeline**: Orchestrates the configuration collection and Git operations
- **Ansible**: Automation framework to interact with network devices
- **Git**: Version control for storing configuration backups

## Network Topology

The solution is designed for a CLOS topology with:
- 2 Spine switches
- 6 Leaf switches

## Files

- `Jenkinsfile`: Defines the Jenkins pipeline for automated execution
- `collect_nexus_configs.py`: PyATS script to collect device configurations
- `testbed.yaml`: Defines the network topology and connection details

## Setup Instructions

### Prerequisites

1. Jenkins server with the following plugins:
   - Git plugin
   - Credentials plugin
   - Pipeline plugin

2. Python 3.6+ with the following packages:
   - pyats
   - pyats.contrib
   - genie
   - gitpython

### Jenkins Credentials Setup

1. Create credentials for Git repository access:
   - ID: `git-credentials`
   - Type: Username with password or SSH key

2. Create credentials for network device access:
   - ID: `pyats-credentials`
   - Type: Username with password

### Configuration

1. Update the `testbed.yaml` file with the correct IP addresses and connection details for your network devices.
2. Update the `GIT_REPO_URL` in the Jenkinsfile to point to your Git repository.

### Running the Pipeline

1. Create a new Jenkins Pipeline job
2. Configure it to use the Jenkinsfile from your repository
3. Schedule the job to run at your desired frequency (e.g., daily)

## How It Works

1. The Jenkins pipeline runs according to its schedule
2. It creates a Python virtual environment and installs the required packages
3. The PyATS script connects to each Nexus device and collects its running configuration
4. Configurations are saved to files with timestamps
5. The files are committed to the Git repository
6. The changes are pushed to the remote repository for backup
