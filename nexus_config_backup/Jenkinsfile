pipeline {
    agent any
    
    environment {
        // Define environment variables
        GIT_CREDENTIALS_ID = 'git-credentials'
        NEXUS_CREDENTIALS_ID = 'nexus-credentials'
        GIT_REPO_URL = 'https://your-git-repo-url.git'
        GIT_BRANCH = 'main'
        CONFIG_DIR = 'nexus_configs'
        DATETIME = sh(script: 'date +"%Y%m%d_%H%M%S"', returnStdout: true).trim()
    }
    
    stages {
        stage('Setup Environment') {
            steps {
                // Clean workspace before starting
                cleanWs()
                
                // Clone the Git repository
                checkout([$class: 'GitSCM', 
                          branches: [[name: "${GIT_BRANCH}"]], 
                          doGenerateSubmoduleConfigurations: false, 
                          extensions: [], 
                          submoduleCfg: [], 
                          userRemoteConfigs: [[credentialsId: "${GIT_CREDENTIALS_ID}", 
                                              url: "${GIT_REPO_URL}"]]])
                
                // Install required Ansible packages
                sh '''
                    # Install Ansible and required collections
                    pip install --upgrade pip
                    pip install ansible
                    ansible-galaxy collection install cisco.nxos
                '''
            }
        }
        
        stage('Collect Nexus Configurations') {
            steps {
                // Use withCredentials to securely handle device login information
                withCredentials([usernamePassword(credentialsId: "${NEXUS_CREDENTIALS_ID}", 
                                                 passwordVariable: 'ANSIBLE_PASSWORD', 
                                                 usernameVariable: 'ANSIBLE_USER')]) {
                    sh '''
                        # Create directory for configurations if it doesn't exist
                        mkdir -p ${CONFIG_DIR}/${DATETIME}
                        
                        # Run Ansible playbook to collect configurations
                        ansible-playbook collect_nexus_configs.yml -e "output_dir=${CONFIG_DIR}"
                        
                        # Check if any configurations were collected
                        CONFIG_COUNT=$(find ${CONFIG_DIR}/${DATETIME} -type f -name "*.txt" | wc -l)
                        if [ $CONFIG_COUNT -eq 0 ]; then
                            echo "No configurations were collected. Failing the build."
                            exit 1
                        fi
                    '''
                }
            }
        }
        
        stage('Commit and Push to Git') {
            steps {
                sh '''
                    # Configure Git user
                    git config user.email "<EMAIL>"
                    git config user.name "Jenkins Automation"
                    
                    # Add all new configuration files
                    git add ${CONFIG_DIR}/${DATETIME}
                    
                    # Commit with meaningful message
                    git commit -m "Automated backup of Nexus configurations on ${DATETIME}"
                    
                    # Push changes to the repository
                    git push origin ${GIT_BRANCH}
                '''
            }
        }
    }
    
    post {
        always {
            // Clean up workspace after job completes
            cleanWs()
        }
        success {
            echo "Successfully collected and backed up Nexus configurations"
        }
        failure {
            echo "Failed to collect or backup Nexus configurations"
        }
    }
}
