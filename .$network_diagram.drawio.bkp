<mxfile host="Electron" agent="Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) draw.io/26.2.15 Chrome/134.0.6998.205 Electron/35.2.1 Safari/537.36" version="26.2.15">
  <diagram name="Page-1" id="c37626ed-c26b-45fb-9056-f9ebc6bb27b6">
    <mxGraphModel dx="1257" dy="748" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="1100" pageHeight="850" background="#ffffff" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="spine2" value="Spine 2" style="sketch=0;points=[[0.015,0.015,0],[0.985,0.015,0],[0.985,0.985,0],[0.015,0.985,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0.25,0],[1,0.5,0],[1,0.75,0],[0.75,1,0],[0.5,1,0],[0.25,1,0],[0,0.75,0],[0,0.5,0],[0,0.25,0]];verticalLabelPosition=bottom;html=1;verticalAlign=top;aspect=fixed;align=center;pointerEvents=1;shape=mxgraph.cisco19.rect;prIcon=nexus_9300;fillColor=#FAFAFA;strokeColor=#005073;" parent="1" vertex="1">
          <mxGeometry x="650" y="120" width="64" height="64" as="geometry" />
        </mxCell>
        <mxCell id="leaf1" value="Leaf 1" style="shape=mxgraph.cisco.switches.workgroup_switch;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="150" y="300" width="64" height="32" as="geometry" />
        </mxCell>
        <mxCell id="leaf2" value="Leaf 2" style="shape=mxgraph.cisco.switches.workgroup_switch;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="300" y="300" width="64" height="32" as="geometry" />
        </mxCell>
        <mxCell id="leaf3" value="Leaf 3" style="shape=mxgraph.cisco.switches.workgroup_switch;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="450" y="300" width="64" height="32" as="geometry" />
        </mxCell>
        <mxCell id="leaf4" value="Leaf 4" style="shape=mxgraph.cisco.switches.workgroup_switch;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="600" y="300" width="64" height="32" as="geometry" />
        </mxCell>
        <mxCell id="leaf5" value="Leaf 5" style="shape=mxgraph.cisco.switches.workgroup_switch;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="750" y="300" width="64" height="32" as="geometry" />
        </mxCell>
        <mxCell id="leaf6" value="Leaf 6" style="shape=mxgraph.cisco.switches.workgroup_switch;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="900" y="300" width="64" height="32" as="geometry" />
        </mxCell>
        <mxCell id="spine1-leaf1" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.25;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#0066CC;" parent="1" source="0YU-USOO_UcQEm4iECsr-1" target="leaf1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="390.48" y="180.79999999999995" as="sourcePoint" />
            <mxPoint x="450" y="170" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine1-leaf2" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#0066CC;" parent="1" source="0YU-USOO_UcQEm4iECsr-1" target="leaf2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="402" y="180.79999999999995" as="sourcePoint" />
            <mxPoint x="192" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine1-leaf3" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;strokeWidth=2;strokeColor=#0066CC;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;" parent="1" source="0YU-USOO_UcQEm4iECsr-1" target="leaf3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="418" y="181" as="sourcePoint" />
            <mxPoint x="342" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine1-leaf4" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#0066CC;" parent="1" source="0YU-USOO_UcQEm4iECsr-1" target="leaf4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="434" y="180.79999999999995" as="sourcePoint" />
            <mxPoint x="492" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine1-leaf5" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#0066CC;" parent="1" source="0YU-USOO_UcQEm4iECsr-1" target="leaf5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="443.5999999999999" y="180.79999999999995" as="sourcePoint" />
            <mxPoint x="642" y="310" as="targetPoint" />
            <Array as="points">
              <mxPoint x="460" y="250" />
              <mxPoint x="782" y="250" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="spine1-leaf6" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.75;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#0066CC;" parent="1" source="0YU-USOO_UcQEm4iECsr-1" target="leaf6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="450" y="180.79999999999995" as="sourcePoint" />
            <mxPoint x="792" y="310" as="targetPoint" />
            <Array as="points">
              <mxPoint x="470" y="230" />
              <mxPoint x="932" y="230" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="spine2-leaf1" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0;exitY=0.95;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#009900;" parent="1" source="spine2" target="leaf1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="650" y="180" as="sourcePoint" />
            <mxPoint x="192" y="310" as="targetPoint" />
            <Array as="points">
              <mxPoint x="650" y="220" />
              <mxPoint x="182" y="220" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="spine2-leaf2" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.1;exitY=0.95;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#009900;" parent="1" source="spine2" target="leaf2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="660" y="181" as="sourcePoint" />
            <mxPoint x="192" y="310" as="targetPoint" />
            <Array as="points">
              <mxPoint x="660" y="240" />
              <mxPoint x="332" y="240" />
            </Array>
          </mxGeometry>
        </mxCell>
        <mxCell id="spine2-leaf3" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.25;exitY=0.95;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#009900;" parent="1" source="spine2" target="leaf3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="666" y="181" as="sourcePoint" />
            <mxPoint x="342" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine2-leaf4" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.5;exitY=0.95;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#009900;" parent="1" source="spine2" target="leaf4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="676" y="181" as="sourcePoint" />
            <mxPoint x="492" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine2-leaf5" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.75;exitY=0.95;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#009900;" parent="1" source="spine2" target="leaf5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="686" y="181" as="sourcePoint" />
            <mxPoint x="642" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine2-leaf6" value="" style="endArrow=none;html=1;rounded=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;exitX=0.9;exitY=0.95;exitDx=0;exitDy=0;exitPerimeter=0;strokeWidth=2;strokeColor=#009900;" parent="1" source="spine2" target="leaf6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="696" y="181" as="sourcePoint" />
            <mxPoint x="792" y="310" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="title" value="Data Center Network - 2 Spine, 6 Leaf Topology (Cisco Nexus)" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1;fontSize=16;" parent="1" vertex="1">
          <mxGeometry x="350" y="40" width="400" height="30" as="geometry" />
        </mxCell>
        <mxCell id="spine_label" value="Spine Layer" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="250" y="120" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="leaf_label" value="Leaf Layer" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="250" y="300" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="legend" value="Connection Legend" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=1" parent="1" vertex="1">
          <mxGeometry x="40" y="40" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="spine1_conn" value="" style="endArrow=none;html=1;rounded=0;strokeWidth=2;strokeColor=#0066CC;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="80" as="sourcePoint" />
            <mxPoint x="100" y="80" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine1_label" value="Spine 1 Connections" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="110" y="65" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="spine2_conn" value="" style="endArrow=none;html=1;rounded=0;strokeWidth=2;strokeColor=#009900;" parent="1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="60" y="110" as="sourcePoint" />
            <mxPoint x="100" y="110" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="spine2_label" value="Spine 2 Connections" style="text;html=1;strokeColor=none;fillColor=none;align=left;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="110" y="95" width="120" height="30" as="geometry" />
        </mxCell>
        <mxCell id="server_rack1" value="" style="shape=mxgraph.cisco.computers_and_peripherals.ibm_mini_as400;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="150" y="400" width="43" height="62" as="geometry" />
        </mxCell>
        <mxCell id="server_rack2" value="" style="shape=mxgraph.cisco.computers_and_peripherals.ibm_mini_as400;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="310" y="400" width="43" height="62" as="geometry" />
        </mxCell>
        <mxCell id="server_rack3" value="" style="shape=mxgraph.cisco.computers_and_peripherals.ibm_mini_as400;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="460" y="400" width="43" height="62" as="geometry" />
        </mxCell>
        <mxCell id="server_rack4" value="" style="shape=mxgraph.cisco.computers_and_peripherals.ibm_mini_as400;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="610" y="400" width="43" height="62" as="geometry" />
        </mxCell>
        <mxCell id="server_rack5" value="" style="shape=mxgraph.cisco.computers_and_peripherals.ibm_mini_as400;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="760" y="400" width="43" height="62" as="geometry" />
        </mxCell>
        <mxCell id="server_rack6" value="" style="shape=mxgraph.cisco.computers_and_peripherals.ibm_mini_as400;html=1;pointerEvents=1;dashed=0;fillColor=#036897;strokeColor=#ffffff;strokeWidth=2;verticalLabelPosition=bottom;verticalAlign=top;align=center;outlineConnect=0;" parent="1" vertex="1">
          <mxGeometry x="910" y="400" width="43" height="62" as="geometry" />
        </mxCell>
        <mxCell id="leaf1-server1" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="leaf1" target="server_rack1" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="510" y="430" as="sourcePoint" />
            <mxPoint x="560" y="380" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="leaf2-server2" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="leaf2" target="server_rack2" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="192" y="342" as="sourcePoint" />
            <mxPoint x="181.5" y="410" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="leaf3-server3" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="leaf3" target="server_rack3" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="202" y="352" as="sourcePoint" />
            <mxPoint x="191.5" y="420" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="leaf4-server4" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="leaf4" target="server_rack4" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="212" y="362" as="sourcePoint" />
            <mxPoint x="201.5" y="430" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="leaf5-server5" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="leaf5" target="server_rack5" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="222" y="372" as="sourcePoint" />
            <mxPoint x="211.5" y="440" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="leaf6-server6" value="" style="endArrow=none;html=1;rounded=0;exitX=0.5;exitY=1;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.5;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;" parent="1" source="leaf6" target="server_rack6" edge="1">
          <mxGeometry width="50" height="50" relative="1" as="geometry">
            <mxPoint x="232" y="382" as="sourcePoint" />
            <mxPoint x="221.5" y="450" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="server_label1" value="Server Rack 1" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="121.5" y="470" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="server_label2" value="Server Rack 2" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="281.5" y="470" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="server_label3" value="Server Rack 3" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="431.5" y="470" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="server_label4" value="Server Rack 4" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="581.5" y="470" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="server_label5" value="Server Rack 5" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="731.5" y="470" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="server_label6" value="Server Rack 6" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;" parent="1" vertex="1">
          <mxGeometry x="881.5" y="470" width="100" height="30" as="geometry" />
        </mxCell>
        <mxCell id="note" value="CLOS Network Topology - Non-Blocking Fabric" style="text;html=1;strokeColor=none;fillColor=none;align=center;verticalAlign=middle;whiteSpace=wrap;rounded=0;fontStyle=2" parent="1" vertex="1">
          <mxGeometry x="400" y="550" width="300" height="30" as="geometry" />
        </mxCell>
        <mxCell id="0YU-USOO_UcQEm4iECsr-1" value="" style="sketch=0;points=[[0.015,0.015,0],[0.985,0.015,0],[0.985,0.985,0],[0.015,0.985,0],[0.25,0,0],[0.5,0,0],[0.75,0,0],[1,0.25,0],[1,0.5,0],[1,0.75,0],[0.75,1,0],[0.5,1,0],[0.25,1,0],[0,0.75,0],[0,0.5,0],[0,0.25,0]];verticalLabelPosition=bottom;html=1;verticalAlign=top;aspect=fixed;align=center;pointerEvents=1;shape=mxgraph.cisco19.rect;prIcon=nexus_9300;fillColor=#FAFAFA;strokeColor=#005073;" vertex="1" parent="1">
          <mxGeometry x="390" y="110" width="50" height="50" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
